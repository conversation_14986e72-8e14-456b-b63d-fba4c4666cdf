<template>
  <div class="advice-editor">
    <div class="editor-header">
      <div class="header-line-number">序号</div>
      <div class="header-content">内容</div>
      <div class="header-actions">
        <a @click="clearAllAdvices">清空</a>
      </div>
    </div>
    <div class="editor-body">
      <!-- 移除拖拽功能，使用普通列表 -->
      <div v-if="!readOnly">
        <div v-for="(element, index) in adviceList" :key="element.id" class="advice-item">
          <div class="advice-item">
            <div class="line-number">
              <span class="number-badge" :class="{ 'empty-content': isEmptyContent(element.content) }">
                {{ index + 1 }}
                <ExclamationCircleOutlined v-if="isEmptyContent(element.content)" class="empty-icon" />
              </span>
            </div>
            <div class="advice-content" :data-index="index">
              <SummaryInput
                v-model="element.content.text"
                @change="(val) => handleContentChange(index, val)"
                @select="(advice) => handleOptionSelect(index, advice)"
                :readOnly="readOnly"
                :ref="
                  (el) => {
                    console.log('🔗 AdviceEditor: ref callback triggered', {
                      index,
                      hasEl: !!el,
                      elType: typeof el,
                      inputRefsLength: inputRefs.value?.length || 0,
                      adviceListLength: adviceList.value.length,
                      elementId: element.id
                    });

                    // 安全地设置ref，确保数组已初始化且索引有效
                    if (el && inputRefs.value && index >= 0 && index < adviceList.value.length) {
                      // 确保数组长度足够
                      while (inputRefs.value.length <= index) {
                        inputRefs.value.push(null);
                        console.log(`📈 AdviceEditor: Extended inputRefs to length ${inputRefs.value.length}`);
                      }
                      inputRefs.value[index] = el;
                      console.log(`✅ AdviceEditor: Set input ref for index ${index}`, {
                        refType: typeof el,
                        hasFocus: typeof el.focus === 'function'
                      });
                    } else {
                      console.warn('❌ AdviceEditor: Failed to set ref', {
                        hasEl: !!el,
                        hasInputRefs: !!inputRefs.value,
                        indexValid: index >= 0 && index < adviceList.value.length,
                        index,
                        adviceListLength: adviceList.value.length
                      });
                    }
                  }
                "
              />
            </div>
            <div class="advice-actions" v-if="!readOnly">
              <a-dropdown :trigger="['click']" placement="bottomRight" v-model:visible="dropdownVisibleMap[element.id]">
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="moveUp" @click="moveUp(index)" :disabled="index === 0" class="move-up-menu-item">
                      <template #icon>
                        <up-outlined />
                      </template>
                      上移
                    </a-menu-item>
                    <a-menu-item key="moveDown" @click="moveDown(index)" :disabled="index === adviceList.length - 1" class="move-down-menu-item">
                      <template #icon>
                        <down-outlined />
                      </template>
                      下移
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="deleteLine(index)" class="delete-menu-item">
                      <template #icon>
                        <minus-outlined />
                      </template>
                      删除
                    </a-menu-item>
                    <a-menu-item key="add" @click="insertLine(index + 1)">
                      <template #icon>
                        <plus-outlined />
                      </template>
                      在下方插入
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="text" size="small" class="action-button">
                  <ellipsis-outlined />
                </a-button>
              </a-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- 只读模式下的静态列表 -->
      <div v-else>
        <div v-for="(element, index) in adviceList" :key="element.id" class="advice-item">
          <div class="line-number">
            <span class="number-badge" :class="{ 'empty-content': isEmptyContent(element.content) }">
              {{ index + 1 }}
              <ExclamationCircleOutlined v-if="isEmptyContent(element.content)" class="empty-icon" />
            </span>
          </div>
          <div class="advice-content" :data-index="index">
            <SummaryInput
              v-model="element.content.text"
              @change="(val) => handleContentChange(index, val)"
              :ref="
                (el) => {
                  // 安全地设置ref，确保数组已初始化且索引有效
                  if (el && inputRefs.value && index >= 0 && index < adviceList.value.length) {
                    // 确保数组长度足够
                    while (inputRefs.value.length <= index) {
                      inputRefs.value.push(null);
                    }
                    inputRefs.value[index] = el;
                    console.log(`AdviceEditor: Set input ref for index ${index} (readonly mode)`);
                  }
                }
              "
              :readonly="readOnly"
            />
          </div>
          <div class="advice-actions" v-if="!readOnly">
            <a-dropdown :trigger="['click']" v-model:visible="dropdownVisibleMap[element.id]">
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="moveUp(index)" :disabled="index === 0" class="move-up-menu-item">
                    <UpOutlined />
                    上移
                  </a-menu-item>
                  <a-menu-item @click="moveDown(index)" :disabled="index === adviceList.length - 1" class="move-down-menu-item">
                    <DownOutlined />
                    下移
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="insertLine(index)">
                    <PlusOutlined />
                    插入
                  </a-menu-item>
                  <a-menu-item @click="deleteLine(index)" class="delete-menu-item">
                    <MinusOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="text" size="small" class="action-button">
                <ellipsis-outlined />
              </a-button>
            </a-dropdown>
          </div>
        </div>
      </div>
    </div>
    <div class="editor-footer" v-if="!readOnly">
      <a-space direction="vertical" style="width: 100%">
        <a-button type="dashed" block @click="addLine" class="add-line-button">
          <plus-outlined />
          添加建议
        </a-button>
      </a-space>

      <!-- AI建议状态指示器 -->
      <a-tooltip v-if="aiSuggestionStatus !== 'idle'">
        <template #title>
          <template v-if="aiSuggestionStatus === 'generating'">正在生成AI建议...</template>
          <template v-else-if="aiSuggestionStatus === 'completed'">AI建议生成完成</template>
          <template v-else-if="aiSuggestionStatus === 'error'">AI建议生成失败</template>
        </template>
        <a-badge :status="aiSuggestionStatusBadge" class="ai-suggestion-badge" />
      </a-tooltip>
    </div>

    <!-- 建议对比弹窗 -->
    <AdviceCompareModal
      v-model:visible="showCompareModal"
      :existingAdvice="existingAdviceForCompare"
      :newAdvice="newAdviceForCompare"
      @confirm="handleCompareConfirm"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, defineEmits, defineProps, nextTick, onMounted, ref, watch } from 'vue';
  import { DownOutlined, EllipsisOutlined, ExclamationCircleOutlined, MinusOutlined, PlusOutlined, UpOutlined } from '@ant-design/icons-vue';

  import SummaryInput from '@/views/summary/components/SummaryInput.vue';
  import AdviceCompareModal from '@/views/summary/components/AdviceCompareModal.vue';
  import { AbnormalSummary, AdviceBean } from '#/types';
  import { message } from 'ant-design-vue';
  import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';
  import signMd5Utils from '@/utils/encryption/signMd5Utils';
  import { getToken } from '@/utils/auth';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createConfirm } = useMessage();

  interface AdviceItem {
    id: string;
    content: AdviceBean;
  }

  const props = defineProps({
    modelValue: {
      type: Array as () => AdviceBean[],
      default: () => [],
    },
    customerReg: {
      type: Object,
      default: null,
    },
    customerSummary: {
      type: Object,
      default: () => ({}),
    },
    // 是否只读模式
    readOnly: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue', 'change', 'save', 'unlock-report', 'get-advice', 'get-ai-summary', 'ai-loading-change']);

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // 建议列表
  const adviceList = ref<AdviceItem[]>([]);

  // 下拉菜单可见性映射
  const dropdownVisibleMap = ref<Record<string, boolean>>({});

  // 输入组件引用数组
  const inputRefs = ref<any[]>([]);

  // 全局设置
  const globSetting = useGlobSetting();

  // AI加载状态
  const loading = ref(false);
  const aiLoading = ref(false);

  // AI建议状态
  const aiSuggestionStatus = ref('idle'); // 'idle', 'generating', 'completed', 'error'

  // 自动隐藏AI建议状态的定时器
  let aiStatusHideTimer: number | null = null;

  // 标记当前是否处于AI流式插入状态
  const isAiStreamInserting = ref(false);

  // 建议对比弹窗相关状态
  const showCompareModal = ref(false);
  const existingAdviceForCompare = ref<AdviceBean | null>(null);
  const newAdviceForCompare = ref<AdviceBean | null>(null);
  const pendingAdviceAction = ref<{
    index: number;
    advice: { label: string; value: string };
  } | null>(null);



  // 计算AI建议状态对应的badge状态
  const aiSuggestionStatusBadge = computed(() => {
    switch (aiSuggestionStatus.value) {
      case 'generating':
        return 'processing';
      case 'completed':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  });

  // 判断建议内容是否不完整
  const isEmptyContent = (content: AdviceBean): boolean => {
    // 使用 valid 属性判断建议是否完整，如果 valid 为 false 或未定义则认为不完整
    return content.valid === false || content.valid === undefined;
  };

  // 初始化数据
  onMounted(() => {
    console.log('🚀 AdviceEditor: Component mounted', {
      hasModelValue: !!props.modelValue,
      modelValueLength: props.modelValue?.length || 0
    });

    // 添加全局错误监听器
    const originalErrorHandler = window.onerror;
    window.onerror = (message, source, lineno, colno, error) => {
      if (message && message.toString().includes('__vnode')) {
        console.error('🚨 AdviceEditor: Caught vnode error', {
          message,
          source,
          lineno,
          colno,
          error,
          adviceListLength: adviceList.value.length,
          inputRefsLength: inputRefs.value.length,
          inputRefsState: inputRefs.value.map((ref, i) => ({ index: i, hasRef: !!ref, type: typeof ref }))
        });
      }
      if (originalErrorHandler) {
        return originalErrorHandler(message, source, lineno, colno, error);
      }
      return false;
    };

    // 初始化输入组件引用数组
    if (props.modelValue && props.modelValue.length > 0) {
      inputRefs.value = new Array(props.modelValue.length);
      console.log('📝 AdviceEditor: Initialized inputRefs with length', props.modelValue.length);

      // 初始化内部数据结构
      adviceList.value = props.modelValue.map((advice) => ({
        id: generateId(),
        content: advice,
      }));
    } else {
      inputRefs.value = [];
      console.log('📝 AdviceEditor: Initialized empty inputRefs');
    }

    if (!props.modelValue || props.modelValue.length == 0) {
      console.log('➕ AdviceEditor: Adding initial line');
      addLine();
    } else {
      // 如果有初始数据，聚焦到第一行
      // 使用nextTick确保DOM已更新
      nextTick(() => {
        // 使用更长的延迟确保组件已完全渲染
        setTimeout(() => {
          focusInput(0);
        }, 200); // 初始化时使用更长的延迟
      });
    }

    // 组件卸载时的清理工作
    return () => {
      console.log('🧹 AdviceEditor: Component unmounting, cleaning up');
      // 清理定时器
      if (aiStatusHideTimer !== null) {
        clearTimeout(aiStatusHideTimer);
        aiStatusHideTimer = null;
      }
      // 恢复原始错误处理器
      window.onerror = originalErrorHandler;
    };
  });

  // 监听外部数据变化
  watch(
    () => props.modelValue,
    (newVal) => {
      // 创建一个函数来比较两个数组是否相等
      const areArraysEqual = (arr1: AdviceBean[], arr2: AdviceItem[]) => {
        if (arr1.length !== arr2.length) return false;

        for (let i = 0; i < arr1.length; i++) {
          const item1 = arr1[i];
          const item2 = arr2[i].content;

          // 比较关键属性
          if (item1.text !== item2.text) return false;
        }

        return true;
      };

      // 如果外部数据与内部数据不同，则更新内部数据
      if (newVal && !areArraysEqual(newVal, adviceList.value)) {
        console.log('AdviceEditor: External data changed, updating internal data');
        adviceList.value = newVal.map((advice) => ({
          id: generateId(),
          content: advice,
        }));

        // 重置输入组件引用数组
        inputRefs.value = new Array(newVal.length);

        // 外部数据变化后调整输入框高度
        nextTick(() => {
          adjustAllInputHeights();
        });
      }
    },
    { deep: true }
  );

  // 监听内部数据变化，更新modelValue
  watch(
    adviceList,
    (newVal) => {
      console.log('🔄 AdviceEditor: watch triggered', {
        newLength: newVal.length,
        currentInputRefsLength: inputRefs.value.length,
        stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
      });

      const contentList = newVal.map((item) => item.content);
      emit('update:modelValue', contentList);
      emit('change', contentList);

      // 安全地调整输入组件引用数组长度，保留现有的有效引用
      const currentLength = inputRefs.value.length;
      const newLength = newVal.length;

      console.log('📊 AdviceEditor: inputRefs adjustment', {
        before: {
          length: currentLength,
          refs: inputRefs.value.map((ref, i) => ({ index: i, hasRef: !!ref, type: typeof ref }))
        }
      });

      if (newLength > currentLength) {
        // 扩展数组，新位置初始化为null
        for (let i = currentLength; i < newLength; i++) {
          inputRefs.value[i] = null;
        }
        console.log(`➕ AdviceEditor: Extended inputRefs from ${currentLength} to ${newLength}`);
      } else if (newLength < currentLength) {
        // 缩减数组，移除多余的引用
        inputRefs.value.splice(newLength);
        console.log(`➖ AdviceEditor: Reduced inputRefs from ${currentLength} to ${newLength}`);
      }

      console.log('📊 AdviceEditor: inputRefs after adjustment', {
        after: {
          length: inputRefs.value.length,
          refs: inputRefs.value.map((ref, i) => ({ index: i, hasRef: !!ref, type: typeof ref }))
        }
      });
    },
    { deep: true }
  );

  // 添加新行
  const addLine = () => {
    console.log('🆕 AdviceEditor: addLine called', {
      currentLength: adviceList.value.length,
      inputRefsLength: inputRefs.value.length
    });

    // 保存当前长度，用于计算新行的索引
    const newIndex = adviceList.value.length;

    // 创建新的AdviceBean对象
    const newAdvice: AdviceBean = {
      seq: (newIndex + 1).toString(),
      name: '',
      content: '',
      text: '',
      chk: false,
      source: 'AdviceEditor3',
      key: generateId(),
      diagnosticCriteria: '',
      valid: false, // 新建议默认为不完整
    };

    console.log('📝 AdviceEditor: Before adding to adviceList', {
      newIndex,
      newAdvice,
      currentAdviceListLength: adviceList.value.length
    });

    // 创建新的数组而不是直接修改
    const newItem = {
      id: generateId(),
      content: newAdvice,
    };

    console.log('📝 AdviceEditor: Creating new array with added item', {
      currentLength: adviceList.value.length,
      newItem
    });

    // 使用扩展运算符创建新数组，避免直接修改原数组
    adviceList.value = [...adviceList.value, newItem];

    console.log('📝 AdviceEditor: After adding to adviceList', {
      newAdviceListLength: adviceList.value.length,
      inputRefsLength: inputRefs.value.length
    });

    // 使用nextTick确保DOM已更新，然后聚焦到新添加的行
    nextTick(() => {
      console.log('⏭️ AdviceEditor: nextTick callback executed', {
        targetIndex: newIndex,
        adviceListLength: adviceList.value.length,
        inputRefsLength: inputRefs.value.length,
        inputRefsState: inputRefs.value.map((ref, i) => ({ index: i, hasRef: !!ref, type: typeof ref }))
      });

      // 使用递归重试机制确保ref已准备好
      const attemptFocus = (retryCount = 0) => {
        const maxRetries = 20; // 增加重试次数
        const retryDelay = 100; // 增加重试间隔

        console.log(`🎯 AdviceEditor: Focus attempt ${retryCount + 1}/${maxRetries}`, {
          targetIndex: newIndex,
          inputRefsLength: inputRefs.value.length,
          targetRef: inputRefs.value[newIndex],
          targetRefType: typeof inputRefs.value[newIndex],
          hasTargetRef: !!inputRefs.value[newIndex]
        });

        if (retryCount >= maxRetries) {
          console.warn(`⚠️ AdviceEditor: Focus timeout after ${maxRetries} attempts, but this is not critical`);
          return;
        }

        // 检查ref是否真的存在且有focus方法
        const targetRef = inputRefs.value[newIndex];
        if (targetRef && typeof targetRef.focus === 'function') {
          try {
            targetRef.focus();
            console.log(`✅ AdviceEditor: Successfully focused input at index ${newIndex} on attempt ${retryCount + 1}`);
            return;
          } catch (error) {
            console.warn(`⚠️ AdviceEditor: Focus error on attempt ${retryCount + 1}:`, error);
          }
        }

        // 继续重试
        setTimeout(() => attemptFocus(retryCount + 1), retryDelay);
      };

      // 延迟更长时间开始尝试聚焦，给Vue更多时间渲染
      setTimeout(() => attemptFocus(), 200);
    });
  };

  // 在指定位置插入新行
  const insertLine = (index: number) => {
    // 创建新的AdviceBean对象
    const newAdvice: AdviceBean = {
      seq: (index + 1).toString(),
      name: '',
      content: '',
      text: '',
      chk: false,
      source: 'AdviceEditor3',
      key: generateId(),
      diagnosticCriteria: '',
      valid: false, // 新建议默认为不完整
    };

    // 创建新项
    const newItem = {
      id: generateId(),
      content: newAdvice,
    };

    // 使用数组方法创建新数组，避免直接修改原数组
    const newArray = [...adviceList.value];
    newArray.splice(index, 0, newItem);
    adviceList.value = newArray;

    console.log(`AdviceEditor: Inserted new line at index ${index}`);

    // 重新排序
    reorderAdviceNumbers();

    // 使用nextTick确保DOM已更新，然后聚焦到新添加的行
    nextTick(() => {
      console.log(`AdviceEditor: DOM updated after inserting line, focusing index ${index}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(index);
      }, 100);
    });
  };

  // 删除行
  const deleteLine = (index: number) => {
    // 如果只有一行，则清空内容而不是删除
    if (adviceList.value.length === 1) {
      adviceList.value[0].content.text = '';
      adviceList.value[0].content.name = '';
      adviceList.value[0].content.content = '';
      adviceList.value[0].content.valid = false; // 清空后标记为不完整
      focusInput(0);
      return;
    }

    // 使用filter创建新数组，避免直接修改原数组
    adviceList.value = adviceList.value.filter((_, i) => i !== index);
    console.log(`AdviceEditor: Deleted line at index ${index}`);

    // 重新排序
    reorderAdviceNumbers();

    // 聚焦到前一行或第一行
    const focusIndex = index > 0 ? index - 1 : 0;
    nextTick(() => {
      setTimeout(() => {
        focusInput(focusIndex);
      }, 100);
    });
  };

  // 上移行
  const moveUp = (index: number) => {
    // 如果已经是第一项，不能再向上移动
    if (index === 0) return;

    // 保存当前项目和其ID
    const currentItem = { ...adviceList.value[index] };
    const currentId = currentItem.id;

    // 计算新的索引
    const newIndex = index - 1;

    // 保存上一项
    const prevItem = { ...adviceList.value[newIndex] };

    console.log(`AdviceEditor: Moving item from index ${index} to ${newIndex}`);

    // 更新seq属性
    currentItem.content.seq = (newIndex + 1).toString();
    prevItem.content.seq = (newIndex + 2).toString();

    // 交换位置
    adviceList.value.splice(newIndex, 2, currentItem, prevItem);

    // 保持下拉菜单打开状态
    keepDropdownOpen(currentId);

    // 使用nextTick确保DOM已更新，然后聚焦到移动后的行
    nextTick(() => {
      console.log(`AdviceEditor: DOM updated after moving item up, focusing index ${newIndex}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(newIndex);
      }, 100);
    });
  };

  // 下移行
  const moveDown = (index: number) => {
    // 如果已经是最后一项，不能再向下移动
    if (index === adviceList.value.length - 1) return;

    // 保存当前项目和其ID
    const currentItem = { ...adviceList.value[index] };
    const currentId = currentItem.id;

    // 计算新的索引
    const newIndex = index + 1;

    // 保存下一项
    const nextItem = { ...adviceList.value[newIndex] };

    console.log(`AdviceEditor: Moving item from index ${index} to ${newIndex}`);

    // 更新seq属性
    currentItem.content.seq = (newIndex + 1).toString();
    nextItem.content.seq = (index + 1).toString();

    // 交换位置
    adviceList.value.splice(index, 2, nextItem, currentItem);

    // 保持下拉菜单打开状态
    keepDropdownOpen(currentId);

    // 使用nextTick确保DOM已更新，然后聚焦到移动后的行
    nextTick(() => {
      console.log(`AdviceEditor: DOM updated after moving item down, focusing index ${newIndex}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(newIndex);
      }, 100);
    });
  };

  // 保持下拉菜单打开状态
  const keepDropdownOpen = (itemId: string) => {
    // 先关闭所有下拉菜单
    Object.keys(dropdownVisibleMap.value).forEach((key) => {
      dropdownVisibleMap.value[key] = false;
    });

    // 然后打开当前项目的下拉菜单
    // 使用nextTick确保在DOM更新后执行
    nextTick(() => {
      dropdownVisibleMap.value[itemId] = true;
    });
  };

  // 聚焦到指定行的输入框
  const focusInput = (index: number) => {
    console.log('🎯 AdviceEditor: focusInput called', {
      targetIndex: index,
      adviceListLength: adviceList.value.length,
      inputRefsLength: inputRefs.value?.length || 0,
      stackTrace: new Error().stack?.split('\n').slice(1, 3).join('\n')
    });

    // 边界检查
    if (index < 0 || index >= adviceList.value.length) {
      console.warn(`❌ AdviceEditor: Cannot focus input at index ${index}, out of range (adviceList length: ${adviceList.value.length})`);
      return false;
    }

    // 检查inputRefs数组是否已初始化且长度正确
    if (!inputRefs.value || index >= inputRefs.value.length) {
      console.warn(`❌ AdviceEditor: inputRefs array not ready for index ${index}`, {
        hasInputRefs: !!inputRefs.value,
        inputRefsLength: inputRefs.value?.length || 0,
        targetIndex: index
      });
      return false;
    }

    const inputRef = inputRefs.value[index];
    console.log('🔍 AdviceEditor: Examining input ref', {
      index,
      hasRef: !!inputRef,
      refType: typeof inputRef,
      hasFocusMethod: inputRef && typeof inputRef.focus === 'function',
      refConstructor: inputRef?.constructor?.name,
      refKeys: inputRef ? Object.keys(inputRef) : []
    });

    if (inputRef && typeof inputRef.focus === 'function') {
      try {
        console.log(`✅ AdviceEditor: Attempting to focus input at index ${index}`);
        inputRef.focus();
        console.log(`🎯 AdviceEditor: Successfully called focus() for index ${index}`);
        return true;
      } catch (error) {
        console.error(`❌ AdviceEditor: Error focusing input at index ${index}:`, {
          error,
          errorMessage: error.message,
          errorStack: error.stack
        });
        return false;
      }
    } else {
      console.warn(`❌ AdviceEditor: Input ref at index ${index} is not available or does not have focus method`, {
        hasRef: !!inputRef,
        refType: typeof inputRef,
        hasFocusMethod: inputRef && typeof inputRef.focus === 'function'
      });
      return false;
    }
  };

  // 调整所有输入框的高度
  const adjustAllInputHeights = () => {
    console.log('AdviceEditor: Adjusting all input heights');

    // 安全检查inputRefs数组
    if (!inputRefs.value || !Array.isArray(inputRefs.value)) {
      console.warn('AdviceEditor: inputRefs is not available for height adjustment');
      return;
    }

    // 遍历所有输入框引用并调整高度
    inputRefs.value.forEach((inputRef, index) => {
      if (inputRef && inputRef.summaryAutoCompleteRef && typeof inputRef.summaryAutoCompleteRef.adjustTextareaHeight === 'function') {
        try {
          inputRef.summaryAutoCompleteRef.adjustTextareaHeight();
          console.log(`AdviceEditor: Adjusted height for input at index ${index}`);
        } catch (error) {
          console.warn(`AdviceEditor: Failed to adjust height for input at index ${index}:`, error);
        }
      } else if (inputRef) {
        console.warn(`AdviceEditor: Input ref at index ${index} does not have adjustTextareaHeight method`);
      }
    });

    // 使用延迟再次调整，确保内容完全渲染
    setTimeout(() => {
      if (!inputRefs.value || !Array.isArray(inputRefs.value)) {
        console.warn('AdviceEditor: inputRefs is not available for second height adjustment attempt');
        return;
      }

      inputRefs.value.forEach((inputRef, index) => {
        if (inputRef && inputRef.summaryAutoCompleteRef && typeof inputRef.summaryAutoCompleteRef.adjustTextareaHeight === 'function') {
          try {
            inputRef.summaryAutoCompleteRef.adjustTextareaHeight();
          } catch (error) {
            console.warn(`AdviceEditor: Failed to adjust height for input at index ${index} (second attempt):`, error);
          }
        }
      });
    }, 100);
  };

  // 处理建议对比弹窗的确认操作
  const handleCompareConfirm = (action: 'keep' | 'replace' | 'addBoth') => {
    if (!pendingAdviceAction.value) return;

    const { index, advice } = pendingAdviceAction.value;
    const content = advice.value;
    const name = advice.label;

    switch (action) {
      case 'keep':
        // 保留现有建议，不做任何操作
        message.info('已保留现有建议');
        break;

      case 'replace':
        // 替换现有建议
        const existingIndex = adviceList.value.findIndex((item) => item.content.name === name);
        if (existingIndex >= 0) {
          adviceList.value[existingIndex].content.text = advice.label + '：' + advice.value;
          adviceList.value[existingIndex].content.name = name;
          adviceList.value[existingIndex].content.content = content;
          adviceList.value[existingIndex].content.source = 'updated';
          message.success('建议已替换');
        }
        break;

      case 'addBoth':
        // 同时保留两个建议，添加新建议
        const firstEmptyIndex = adviceList.value.findIndex((item) => !item.content.text);
        if (firstEmptyIndex >= 0) {
          // 如果存在空行，使用空行
          adviceList.value[firstEmptyIndex].content.text = advice.label + '：' + advice.value;
          adviceList.value[firstEmptyIndex].content.name = name + '_新';
          adviceList.value[firstEmptyIndex].content.content = content;
          adviceList.value[firstEmptyIndex].content.source = 'duplicate';
          // 聚焦到空行
          nextTick(() => {
            setTimeout(() => {
              focusInput(firstEmptyIndex);
            }, 100);
          });
        } else {
          // 添加新行
          adviceList.value.push({
            id: generateId(),
            content: {
              seq: (adviceList.value.length + 1).toString(),
              name: name + '_新',
              content: content,
              text: advice.label + '：' + advice.value,
              chk: false,
              source: 'duplicate',
              key: generateId(),
              diagnosticCriteria: '',
            },
          });
        }
        message.success('已添加新建议');
        break;
    }

    // 清理状态
    pendingAdviceAction.value = null;
    existingAdviceForCompare.value = null;
    newAdviceForCompare.value = null;
  };

  // 处理内容变化
  const handleContentChange = (index: number, value: string) => {
    if (index < 0 || index >= adviceList.value.length) return;

    // 更新内容
    adviceList.value[index].content.text = value;

    // 解析内容，提取名称和内容
    const parts = value.split('：');
    if (parts.length > 1) {
      // 移除可能的序号
      const name = parts[0].replace(/^\d+\.\s*/, '').trim();
      const content = parts.slice(1).join('：').trim();

      adviceList.value[index].content.name = name;
      adviceList.value[index].content.content = content;
    } else {
      // 如果没有冒号，则整个内容作为content
      adviceList.value[index].content.content = value;
      adviceList.value[index].content.name = `建议${index + 1}`;
    }

    // 更新 valid 属性：如果有内容则标记为完整，否则为不完整
    adviceList.value[index].content.valid = value.trim() !== '';

    // 如果内容为空，并且不是唯一一行，则删除该行
    if (value.trim() === '' && adviceList.value.length > 1) {
      deleteLine(index);
    }
  };

  // 处理选项选择
  const handleOptionSelect = (index: number, advice: { label: string; value: string }) => {
    if (index < 0 || index >= adviceList.value.length) return;

    console.log('AdviceEditor3: handleOptionSelect', index, advice);

    let content = advice.value;
    let name = advice.label;

    const editingIndex = adviceList.value.findIndex((item, idx) => {
      // 排除当前行，检查其他行是否有相同的name
      return name.includes(item.content.text) && idx == index;
    });
    if (editingIndex >= 0) {
      adviceList.value[editingIndex].content.text = advice.label + '：' + advice.value;
      adviceList.value[editingIndex].content.name = name;
      adviceList.value[editingIndex].content.content = content;
      adviceList.value[editingIndex].content.valid = true; // 选择建议后标记为完整
      return;
    } else {
      // 检查是否存在重复的建议（name相同）
      const existingIndex = adviceList.value.findIndex((item, idx) => {
        // 排除当前行，检查其他行是否有相同的name
        return item.content.name == name;
      });

      if (existingIndex >= 0) {
        // 如果存在重复，显示详细对比弹窗
        const existingAdvice = adviceList.value[existingIndex].content;
        const newAdvice: AdviceBean = {
          seq: (index + 1).toString(),
          name: name,
          content: content,
          text: advice.label + '：' + advice.value,
          chk: false,
          source: 'new',
          key: generateId(),
          diagnosticCriteria: '',
        };

        // 设置对比数据
        existingAdviceForCompare.value = existingAdvice;
        newAdviceForCompare.value = newAdvice;
        pendingAdviceAction.value = { index, advice };

        // 显示对比弹窗
        showCompareModal.value = true;
      } else {
        let firstEmptyIndex = adviceList.value.findIndex((item) => !item.content.text);
        if (firstEmptyIndex >= 0) {
          // 如果存在空行，使用空行
          adviceList.value[firstEmptyIndex].content.text = advice.label + '：' + advice.value;
          adviceList.value[firstEmptyIndex].content.name = name;
          adviceList.value[firstEmptyIndex].content.content = content;
          adviceList.value[firstEmptyIndex].content.valid = true; // 选择建议后标记为完整
          // 聚焦到空行
          nextTick(() => {
            setTimeout(() => {
              focusInput(firstEmptyIndex);
            }, 100);
          });
        } else {
          adviceList.value.push({
            id: generateId(),
            content: {
              seq: (adviceList.value.length + 1).toString(),
              name: name,
              content: content,
              text: advice.label + '：' + advice.value,
              chk: false,
              source: 'AdviceEditor3',
              key: generateId(),
              diagnosticCriteria: '',
              valid: true, // 选择建议后标记为完整
            },
          });
          // 聚焦到新行
          nextTick(() => {
            setTimeout(() => {
              focusInput(firstEmptyIndex);
            }, 100);
          });
        }
      }
    }
  };

  // 重新排序建议序号
  const reorderAdviceNumbers = () => {
    adviceList.value.forEach((item, index) => {
      item.content.seq = (index + 1).toString();
    });
  };



  // 加载建议数据 - 兼容老版和新版数据结构
  const loadAdvices = (advices: any[]) => {
    console.log('AdviceEditor: Loading advices:', advices);
    if (Array.isArray(advices) && advices.length > 0) {
      // 将数据转换为内部格式，同时兼容老版和新版数据结构
      adviceList.value = advices.map((advice, index) => {
        // 检测数据结构类型
        const isNewFormat = advice.hasOwnProperty('text') && advice.hasOwnProperty('key');

        let processedAdvice: AdviceBean;

        if (isNewFormat) {
          // 新版数据结构 - 直接使用
          processedAdvice = {
            name: advice.name || '',
            content: advice.content || '',
            text: advice.text || `${advice.name || ''}：${advice.content || ''}`,
            seq: advice.seq || (index + 1).toString(),
            chk: advice.chk || false,
            source: advice.source || 'editor',
            key: advice.key || generateId(),
            diagnosticCriteria: advice.diagnosticCriteria || '',
            valid: advice.valid !== undefined ? advice.valid : advice.content && advice.content.trim() !== '' ? true : false,
          };
        } else {
          // 老版数据结构 - 转换为新版格式
          const name = advice.name || '';
          const content = advice.content || '';
          const text = name && content ? `${name}：${content}` : name || content || '';

          processedAdvice = {
            name: name,
            content: content,
            text: text,
            seq: advice.seq ? advice.seq.toString() : (index + 1).toString(),
            chk: advice.chk || false,
            source: advice.source || 'editor',
            key: generateId(), // 老版数据没有key，生成新的
            diagnosticCriteria: advice.diagnosticCriteria || '',
            valid: content && content.trim() !== '' ? true : false, // 老版数据根据content是否有内容判断完整性
          };
        }

        return {
          id: generateId(),
          content: processedAdvice,
        };
      });

      console.log('AdviceEditor: Processed advices:', adviceList.value);

      // 加载数据后调整所有输入框的高度
      nextTick(() => {
        adjustAllInputHeights();
      });
    } else {
      // 如果没有数据，添加一个空行
      clearContent();
    }
  };

  // 清空内容
  const clearContent = () => {
    console.log('AdviceEditor: Clearing content');
    adviceList.value = [];
    addLine(); // 添加一个空行
  };

  // 清空全部建议（带确认）
  const clearAllAdvices = () => {
    createConfirm({
      iconType: 'warning',
      title: '确认清空全部建议',
      content: '确定要清空所有建议内容吗？此操作不可撤销。',
      onOk: () => {
        clearContent();
        message.success('已清空所有建议');
      },
    });
  };

  // 获取结构化内容
  const getStructuredContent = () => {
    return adviceList.value.map((item) => ({
      seq: item.content.seq,
      name: item.content.name,
      content: item.content.content,
      text: item.content.text,
      chk: item.content.chk,
      source: item.content.source,
      key: item.content.key,
      diagnosticCriteria: item.content.diagnosticCriteria || '',
    }));
  };

  // 检查是否有非空的建议内容
  const hasNonEmptyContent = () => {
    return adviceList.value.some((item) => {
      const content = item.content;
      return (
        (content.text && content.text.trim() !== '') ||
        (content.name && content.name.trim() !== '') ||
        (content.content && content.content.trim() !== '')
      );
    });
  };

  // 保存建议
  const saveAdvice = () => {
    const adviceData = getStructuredContent();
    const adviceText = adviceData.map((item) => `${item.name}：${item.content}`).join('\n');

    emit('save', {
      summaryJson: adviceData,
      adviceText: adviceText,
    });
  };

  // AI流式构建总检建议
  const getAiSummary = async (abnormalSummary: AbnormalSummary[]) => {
    // --- 2. Prepare for AI Request ---
    loading.value = true;
    aiLoading.value = true;
    aiSuggestionStatus.value = 'generating';
    // 设置AI流式插入状态为激活
    isAiStreamInserting.value = true;

    // 通知父组件loading状态变化
    emit('ai-loading-change', true);

    // 清除之前的定时器
    if (aiStatusHideTimer !== null) {
      clearTimeout(aiStatusHideTimer);
      aiStatusHideTimer = null;
    }

    // --- 3. Clear Editor Before Request ---
    try {
      // 清空现有内容
      clearContent();
    } catch (clearError) {
      console.error('清空编辑器时出错 (Error clearing editor):', clearError);
      loading.value = false;
      aiLoading.value = false;
      // 通知父组件loading状态变化
      emit('ai-loading-change', false);
      return;
    }

    // --- 4. Build Request and Define Callbacks ---
    try {
      console.log('Input AbnormalSummary:', abnormalSummary);
      const abnormalSummaryMessage = abnormalSummary?.map((item) => item.text || '').join('\n') || '';

      // Optional: Handle empty message case
      if (!abnormalSummaryMessage) {
        message.warning('异常汇总内容为空，无法生成建议');
        loading.value = false;
        aiLoading.value = false;
        // 通知父组件loading状态变化
        emit('ai-loading-change', false);
        aiSuggestionStatus.value = 'error';
        isAiStreamInserting.value = false;
        return;
      }

      interface AiSummaryRequestBody {
        messages: { role: 'user' | 'assistant'; content: string }[];
        modId: string;
      }

      const requestBody: AiSummaryRequestBody = {
        messages: [{ role: 'user', content: abnormalSummaryMessage }],
        modId: '1888072265078280195',
      };

      interface UseXAgentCallbacks {
        onSuccess: (finalResult: string) => void;
        onUpdate: (chunk: string) => void;
        onError: (error: Error) => void;
      }

      const callbacks: UseXAgentCallbacks = {
        onSuccess: () => {
          console.log('AI suggestion stream finished successfully.');
          loading.value = false;
          aiLoading.value = false;

          // 通知父组件loading状态变化
          emit('ai-loading-change', false);

          // 更新状态为完成
          aiSuggestionStatus.value = 'completed';

          // 重置AI流式插入状态
          isAiStreamInserting.value = false;

          // 设置定时器，5秒后自动隐藏状态指示条
          aiStatusHideTimer = window.setTimeout(() => {
            aiSuggestionStatus.value = 'idle';
            aiStatusHideTimer = null;
          }, 5000);
        },
        onUpdate: async (chunk) => {
          // --- 5. Process Streaming Update ---
          console.log('Received stream chunk:', chunk);
          let parsedData: any = null;

          try {
            // 尝试解析JSON
            try {
              parsedData = JSON.parse(chunk);
            } catch (jsonError) {
              // 如果解析失败，尝试提取JSON部分
              const jsonMatch = chunk.match(/\{.*\}/);
              if (jsonMatch) {
                try {
                  parsedData = JSON.parse(jsonMatch[0]);
                } catch (extractError) {
                  console.error('Failed to extract JSON from chunk:', extractError);
                }
              } else {
                console.error('Could not parse chunk as single JSON or extract multiple JSONs.', 'Chunk:', chunk);
                return; // Stop processing this chunk
              }
            }

            // --- 6. Update Editor Content ---
            if (!parsedData) {
              console.warn('Parsed data is empty or null after processing chunk.');
              return;
            }

            try {
              // 创建新的AdviceBean对象
              const newAdvice: AdviceBean = {
                seq: parsedData.seq.toString(),
                name: parsedData.name,
                content: parsedData.content,
                text: `${parsedData.name}：${parsedData.content}`,
                chk: false,
                source: 'AI',
                key: generateId(),
                diagnosticCriteria: parsedData.diagnosticCriteria || '',
                valid: true, // AI生成的建议标记为完整
              };

              // 检查是否已存在相同序号的建议
              const existingIndex = adviceList.value.findIndex((item) => parseInt(item.content.seq) === parseInt(parsedData.seq.toString()));

              if (existingIndex >= 0) {
                // 更新现有建议
                adviceList.value[existingIndex].content = newAdvice;
              } else {
                // 添加新建议
                const seqNumber = parseInt(parsedData.seq.toString());
                if (seqNumber > 0 && seqNumber <= adviceList.value.length + 1) {
                  // 在指定位置插入
                  adviceList.value.splice(seqNumber - 1, 0, {
                    id: generateId(),
                    content: newAdvice,
                  });
                } else {
                  // 添加到末尾
                  adviceList.value.push({
                    id: generateId(),
                    content: newAdvice,
                  });
                }
              }

              // 重新排序
              reorderAdviceNumbers();
            } catch (renderError) {
              console.error('Error updating advice list:', renderError);
            }
          } catch (error) {
            console.error('处理流式更新失败 (Failed to process stream update chunk):', error, 'Chunk:', chunk);
          }
        }, // end onUpdate

        onError: (error) => {
          console.error('AI建议生成失败 (AI suggestion generation failed):', error);
          const errorMessage = error?.message || '未知错误 (Unknown error)';
          message.error(`AI建议生成失败 (AI suggestion failed): ${errorMessage}`);
          loading.value = false;
          aiLoading.value = false;

          // 通知父组件loading状态变化
          emit('ai-loading-change', false);

          // 更新状态为错误
          aiSuggestionStatus.value = 'error';

          // 重置AI流式插入状态
          isAiStreamInserting.value = false;

          // 设置定时器，5秒后自动隐藏状态指示条
          aiStatusHideTimer = window.setTimeout(() => {
            aiSuggestionStatus.value = 'idle';
            aiStatusHideTimer = null;
          }, 5000);
        },
      }; // end callbacks object

      // --- 7. Initiate Request ---
      console.log('Sending AI request with body:', requestBody);
      request(requestBody, callbacks);
    } catch (error) {
      console.error('发送AI建议请求前出错 (Error before sending AI suggestion request):', error);
      message.error('准备AI请求失败，请重试 (Failed to prepare AI request, please retry)');
      loading.value = false;
      aiLoading.value = false;

      // 通知父组件loading状态变化
      emit('ai-loading-change', false);

      // 更新状态为错误
      aiSuggestionStatus.value = 'error';

      // 设置定时器，5秒后自动隐藏状态指示条
      aiStatusHideTimer = window.setTimeout(() => {
        aiSuggestionStatus.value = 'idle';
        aiStatusHideTimer = null;
      }, 5000);
    }
  };

  let currentAbortController: AbortController | null = null;
  const currentRequestCancelHandler = ref<(() => void) | null>(null);
  const request = (requestBody: any, callbacks: any) => {
    loading.value = true;
    currentAbortController = new AbortController();
    const abortController = currentAbortController;

    const sign = signMd5Utils.getSign('/summary/customerRegSummary/ai/streamSummary', null);
    const token = getToken();
    let accumulatedData = '';
    let streamClosed = false;
    let hasReceivedData = false;

    fetchEventSource(globSetting.apiUrl + '/summary/customerRegSummary/ai/streamSummary', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
        'X-Sign': sign,
        'X-TIMESTAMP': String(signMd5Utils.getTimestamp()),
        'X-Access-Token': token || '',
      },
      body: JSON.stringify(requestBody),
      signal: abortController.signal,

      async onopen(response) {
        if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
          loading.value = false;
          console.log('[fetchEventSource] Connection opened');
        } else {
          const errorText = await response.text();
          throw new Error(`[fetchEventSource] Failed to connect: ${response.status} - ${errorText || response.statusText}`);
        }
      },

      onmessage(msg: EventSourceMessage) {
        console.log(`[fetchEventSource] Received event: ${msg.event}, data: ${msg.data}`);
        const data = msg.data;

        if (streamClosed) {
          console.warn('[fetchEventSource] Ignoring message received after stream closure signal.');
          return;
        }

        hasReceivedData = true;

        if (msg.event === 'update' || msg.event === 'message') {
          if (msg.data) {
            try {
              console.log('当前数据:', JSON.parse(data));
              // 只传递当前数据块，而不是累积的数据
              // 这样可以避免重复处理已经处理过的内容
              callbacks.onUpdate(data);
              // 仍然累积数据用于最终的成功回调
              accumulatedData += data;
            } catch (error) {
              console.error('处理消息数据失败:', error);
            }
          }
        } else if (msg.event === 'done') {
          streamClosed = true;

          callbacks.onSuccess(accumulatedData);
          console.log('完整的建议数据:', accumulatedData);
          abortController.abort('Stream completed with done event');
        } else if (msg.event === 'custom_error' || msg.event === 'error') {
          streamClosed = true;

          const errorMessage = msg.data || 'Server signaled an error via SSE event';
          callbacks.onError(new Error(errorMessage));
          abortController.abort('Server sent error event');
        }
      },

      onclose() {
        console.log('[fetchEventSource] Connection closed.');
        if (!streamClosed) {
          streamClosed = true;

          if (abortController.signal.aborted) {
            console.log('[fetchEventSource] Connection closed due to abort.');
            const abortReason = abortController.signal.reason?.toString() || '';

            if (abortReason.includes('Stream completed') || abortReason.includes('done event')) {
              console.log('[fetchEventSource] Stream was properly terminated');
            } else {
              callbacks.onError(new DOMException('请求已取消', 'AbortError'));
            }
          } else if (hasReceivedData) {
            console.warn('[fetchEventSource] Connection closed unexpectedly, but data was received. Treating as success.');
            callbacks.onSuccess(accumulatedData);
          } else {
            console.error('[fetchEventSource] Connection closed unexpectedly without receiving any data.');
            callbacks.onError(new Error('连接意外关闭，未收到任何数据'));
          }
        }
      },

      onerror(err) {
        console.error('[fetchEventSource] Error:', err);

        if (!streamClosed) {
          streamClosed = true;

          if (hasReceivedData && accumulatedData) {
            console.warn('[fetchEventSource] Error occurred after receiving data. Treating as partial success.');
            accumulatedData += '\n\n*注意: 获取完整回复时出现网络中断，上述内容可能不完整。*';
            callbacks.onSuccess(accumulatedData);
          } else {
            const errorMessage =
              err instanceof Error
                ? err.name === 'TypeError' && err.message.includes('network')
                  ? '网络连接中断，请检查网络后重试'
                  : err.message
                : '请求过程中发生错误';

            callbacks.onError(new Error(errorMessage));
          }
        }

        if (err instanceof TypeError && (err.message.includes('network') || err.message.includes('chunked') || err.message.includes('fetch'))) {
          throw err;
        }
      },
    });

    const cancelRequest = () => {
      if (!streamClosed && currentAbortController === abortController) {
        console.log('[fetchEventSource] Cancellation requested.');
        abortController.abort('User canceled request');
        streamClosed = true;

        currentRequestCancelHandler.value = null;
      }
    };

    currentRequestCancelHandler.value = cancelRequest;
    return cancelRequest;
  };

  // 从异常汇总添加建议
  const addAdviceFromAbnormal = (data: { abnormalSummary: AbnormalSummary; advice: { label: string; value: string } }) => {
    console.log('从异常汇总添加建议:', data);

    if (!data || !data.abnormalSummary || !data.advice) {
      console.warn('Invalid advice data:', data);
      return;
    }

    // 创建新的建议内容
    const newAdvice: AdviceBean = {
      seq: (adviceList.value.length + 1).toString(),
      name: data.advice.label,
      content: data.advice.value,
      text: `${data.advice.label}：${data.advice.value}`,
      chk: false,
      source: 'AbnormalSummary',
      key: generateId(),
      diagnosticCriteria: '',
      valid: data.advice.value && data.advice.value.trim() !== '' ? true : false, // 根据内容判断完整性
    };

    // 检查是否已存在相同异常项的建议
    const existingIndex = adviceList.value.findIndex((item) => {
      // 检查名称是否包含异常项的标题
      return item.content.name.includes(data.advice.label);
    });

    if (existingIndex >= 0) {
      // 如果已存在，显示详细对比弹窗
      const existingAdvice = adviceList.value[existingIndex].content;

      // 设置对比数据
      existingAdviceForCompare.value = existingAdvice;
      newAdviceForCompare.value = newAdvice;
      pendingAdviceAction.value = {
        index: existingIndex,
        advice: data.advice,
      };

      // 显示对比弹窗
      showCompareModal.value = true;
    } else {
      // 在不存在重复的情况下，从头遍历查找第一个text为空的进行替换
      let emptyIndex = -1;
      for (let i = 0; i < adviceList.value.length; i++) {
        const item = adviceList.value[i];
        if (!item.content.text || item.content.text.trim() === '') {
          emptyIndex = i;
          break;
        }
      }

      if (emptyIndex >= 0) {
        // 找到第一个空的建议，直接替换
        adviceList.value[emptyIndex].content = newAdvice;
        message.success('已添加建议');
      } else {
        // 没有找到空的建议，添加新建议
        // 查找与异常汇总相关的建议位置
        const relatedIndex = adviceList.value.findIndex((item) => {
          return item.content.name.includes(data.abnormalSummary.title);
        });

        if (relatedIndex >= 0) {
          // 在相关建议后插入
          adviceList.value.splice(relatedIndex + 1, 0, {
            id: generateId(),
            content: newAdvice,
          });
        } else {
          // 添加到末尾
          adviceList.value.push({
            id: generateId(),
            content: newAdvice,
          });
        }

        message.success('已添加建议');
      }
    }

    // 重新排序
    reorderAdviceNumbers();
  };

  // 从知识库添加建议
  const addAdviceFromKnowledgeBase = (advice: { label: string; value: string }) => {
    console.log('AdviceEditor3: Adding advice from knowledge base:', advice);

    if (!advice || !advice.label || !advice.value) {
      console.warn('Invalid advice data:', advice);
      return;
    }

    // 创建新的建议内容
    const newAdvice: AdviceBean = {
      seq: (adviceList.value.length + 1).toString(),
      name: advice.label,
      content: advice.value,
      text: `${advice.label}：${advice.value}`,
      chk: false,
      source: 'KnowledgeBase',
      key: generateId(),
    };

    // 检查是否已存在相同标题的建议
    const existingIndex = adviceList.value.findIndex((item) => {
      // 检查名称是否相同
      return item.content.name === advice.label;
    });

    if (existingIndex >= 0) {
      // 如果已存在，显示详细对比弹窗
      const existingAdvice = adviceList.value[existingIndex].content;

      // 设置对比数据
      existingAdviceForCompare.value = existingAdvice;
      newAdviceForCompare.value = newAdvice;
      pendingAdviceAction.value = {
        index: existingIndex,
        advice: advice,
      };

      // 显示对比弹窗
      showCompareModal.value = true;
    } else {
      // 检查最后一个建议是否为空
      const lastIndex = adviceList.value.length - 1;
      const lastItem = lastIndex >= 0 ? adviceList.value[lastIndex] : null;
      const isLastEmpty =
        lastItem &&
        (!lastItem.content.text || lastItem.content.text.trim() === '') &&
        (!lastItem.content.name || lastItem.content.name.trim() === '') &&
        (!lastItem.content.content || lastItem.content.content.trim() === '');

      if (isLastEmpty) {
        // 如果最后一个建议为空，直接使用它
        adviceList.value[lastIndex].content = newAdvice;
        message.success('已添加建议');
      } else {
        // 添加到末尾
        adviceList.value.push({
          id: generateId(),
          content: newAdvice,
        });
        message.success('已添加建议');
      }
    }

    // 重新排序
    reorderAdviceNumbers();
  };

  // 向父组件暴露方法
  defineExpose({
    loadAdvices,
    clearContent,
    clearAllAdvices,
    getStructuredContent,
    hasNonEmptyContent,
    saveAdvice,
    reorderAdviceNumbers,
    getAiSummary,
    addAdviceFromAbnormal,
    addAdviceFromKnowledgeBase,
    adjustAllInputHeights,
  });
</script>

<style scoped>
  .advice-editor {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .editor-header {
    display: flex;
    background-color: #fafafa;
    padding: 6px 4px;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    font-size: 11px;
    color: #666;
  }

  .header-line-number {
    width: 30px;
    flex-shrink: 0;
    padding-left: 4px;
    font-size: 11px;
  }

  .header-content {
    flex: 1;
  }

  .header-actions {
    width: 50px;
    display: flex;
    justify-content: flex-end;
    padding-right: 4px;
  }

  .header-action-item {
    font-size: 10px;
    color: #999;
    width: 20px;
    text-align: center;
  }

  .header-clear-button {
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    color: #ff4d4f;
    border: none;
    background: transparent;
    font-size: 12px;
  }

  .header-clear-button:hover {
    color: #ff7875;
    background-color: rgba(255, 77, 79, 0.1);
    border-radius: 2px;
  }

  .header-clear-button:focus {
    color: #ff4d4f;
    background-color: rgba(255, 77, 79, 0.1);
    border-radius: 2px;
  }

  .editor-body {
    padding: 6px 6px 10px;
  }

  .advice-item {
    display: flex;
    margin-bottom: 6px;
    align-items: flex-start;
    padding: 4px 0;
    transition:
      background-color 0.2s ease,
      border-color 0.2s ease;
    border-radius: 6px;
    border: 1px solid transparent;
    position: relative;
  }

  .advice-item:hover {
    background-color: #fafafa;
    border-color: #f0f0f0;
  }



  /* 拖拽状态样式 */
  .ghost-item {
    opacity: 0.5;
    background: #f0f9ff !important;
    border: 2px dashed #1890ff !important;
    border-radius: 6px;
  }

  .chosen-item {
    background-color: #e6f7ff !important;
    border: 1px solid #1890ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15) !important;
  }



  .line-number {
    width: 30px;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 2px;
  }

  .number-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 50%;
    font-size: 10px;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
  }

  .number-badge.empty-content {
    background-color: #fff2e8;
    border: 1px solid #ffab00;
    color: #d46b08;
  }

  .empty-icon {
    position: absolute;
    top: -2px;
    right: -2px;
    font-size: 8px;
    color: #ff7875;
    background-color: #fff;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .advice-content {
    flex: 1;
    margin: 0 4px;
    min-height: 24px;
  }

  .advice-actions {
    width: 30px;
    display: flex;
    justify-content: flex-end;
    padding-top: 2px;
  }

  .action-button {
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    color: #999;
  }

  .action-button:hover {
    color: #1890ff;
    background-color: #f0f0f0;
  }

  .editor-footer {
    padding: 8px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }

  .add-line-button {
    flex: 1;
  }

  .ai-button {
    background-color: #52c41a;
    border-color: #52c41a;
  }

  .ai-button:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }

  .ai-suggestion-badge {
    margin-left: 8px;
  }
</style>
